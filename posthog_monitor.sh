#!/bin/bash
#
# This script continuously monitors VM statistics, aggregates them over a defined
# interval, and sends the calculated stats (mean, min, max) to PostHog.
# It uses only standard shell utilities with no external dependencies.
# It outputs all logs in a structured JSON format.

# --- Configuration ---

# Exit immediately if a command fails or a variable is unset.
set -e
set -u
set -o pipefail

# -- Script Behavior --
# How long (in seconds) to collect data before sending to PostHog. 60-300 is a good range.
AGGREGATION_INTERVAL=60
# How often (in seconds) to sample the metrics within the interval.
SAMPLE_RATE=5

# -- PostHog Configuration --
# Load API Key from environment variable. Export this before running.
# export POSTHOG_API_KEY="phc_..."
if [ -z "${POSTHOG_API_KEY:-}" ]; then
  echo '{"level":"error","message":"The POSTHOG_API_KEY environment variable is not set."}' >&2
  exit 1
fi
POSTHOG_HOST="https://eu.i.posthog.com"
DISTINCT_ID=$(hostname)

# --- Logging Function ---
# A robust function to print logs in JSON format.
log() {
  local level="$1"
  local message="$2"
  echo "{\"level\":\"$level\",\"timestamp\":\"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\",\"host\":\"$DISTINCT_ID\",\"message\":\"$message\"}"
}

# --- Statistics Calculation Function ---
# Calculates mean, min, and max using only awk.
# awk is perfect for this as it handles floating-point numbers and is a standard utility.
calc_stats() {
  local samples=("$@")
  local sample_count=${#samples[@]}

  # If there are no samples, return zeros to prevent errors.
  if [ "$sample_count" -eq 0 ]; then
    echo "0 0 0" # mean min max
    return
  fi

  # Use a single awk command to process all samples and output sum, min, and max.
  # This is highly efficient.
  # Input is passed to awk using a HEREDOC with process substitution.
  local stats_raw=$(awk '
    BEGIN {
      # Initialize min to a very large number and max to a very small one
      min = 999999999;
      max = -999999999;
      sum = 0;
    }
    {
      sum += $1;
      if ($1 < min) { min = $1; }
      if ($1 > max) { max = $1; }
    }
    END {
      # Handle case where no data was piped in
      if (NR == 0) {
        print 0, 0, 0;
      } else {
        # awk handles floating point division automatically for the mean
        mean = sum / NR;
        print mean, min, max;
      }
    }
  ' < <(printf "%s\n" "${samples[@]}"))

  # Echo the results so they can be captured by 'read'
  echo "$stats_raw"
}


# --- Main Monitoring Loop ---
log "info" "Starting VM monitoring script with a ${AGGREGATION_INTERVAL}s interval."

while true; do
  # Arrays to store the metric samples for this interval
  cpu_samples=()
  mem_used_samples=()
  
  collection_end_time=$(( $(date +%s) + AGGREGATION_INTERVAL ))

  # --- Data Collection Phase ---
  while [ "$(date +%s)" -lt "$collection_end_time" ]; do
    cpu_samples+=( $(awk '{print $1}' /proc/loadavg) )
    mem_used_samples+=( $(free -m | awk '/^Mem:/ {print $3}') )
    sleep "$SAMPLE_RATE"
  done

  # --- Aggregation Phase ---
  log "info" "Aggregating ${#cpu_samples[@]} samples..."
  
  # Calculate stats for each metric. The function returns mean, min, and max.
  read -r cpu_mean cpu_min cpu_max <<< $(calc_stats "${cpu_samples[@]}")
  read -r mem_used_mean mem_used_min mem_used_max <<< $(calc_stats "${mem_used_samples[@]}")
  
  # Get single-point-in-time metrics that don't need aggregation
  DISK_USAGE_NUMERIC=$(df --output=pcent / | tail -n 1 | tr -d ' %')
  MEM_TOTAL=$(free -m | awk '/^Mem:/ {print $2}')


  # --- Construct JSON Payload ---
  JSON_PAYLOAD=$(cat <<EOF
{
  "api_key": "$POSTHOG_API_KEY",
  "event": "vm_stats_aggregated",
  "distinct_id": "$DISTINCT_ID",
  "properties": {
    "source": "vm_aggregator_script",
    "aggregation_interval_seconds": $AGGREGATION_INTERVAL,
    "sample_count": ${#cpu_samples[@]},
    "cpu_load_1m_mean": $cpu_mean,
    "cpu_load_1m_max": $cpu_max,
    "cpu_load_1m_min": $cpu_min,
    "memory_used_mb_mean": $mem_used_mean,
    "memory_used_mb_max": $mem_used_max,
    "memory_used_mb_min": $mem_used_min,
    "memory_total_mb": $MEM_TOTAL,
    "disk_root_used_percent": $DISK_USAGE_NUMERIC
  }
}
EOF
)

  # --- Send Data to PostHog ---
  log "info" "Sending aggregated metrics to PostHog."
  
  if curl -s -S -X POST "$POSTHOG_HOST/capture/" \
       -H "Content-Type: application/json" \
       -d "$JSON_PAYLOAD" > /dev/null; then
    log "info" "Successfully sent stats for interval."
  else
    log "error" "Failed to send stats to PostHog."
  fi

done