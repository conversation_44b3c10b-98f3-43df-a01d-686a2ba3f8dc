// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { seoPlugin } from '@payloadcms/plugin-seo'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { buildConfig } from 'payload'
import sharp from 'sharp'
import path from 'path'
import { fileURLToPath } from 'url'

import Categories from './collections/Categories'
import Features from './collections/Features'
import GeneratedImages from './collections/GeneratedImages'
import { Media } from './collections/Media'
import Posts from './collections/Posts'
import PublicUsers from './collections/PublicUsers/PublicUsers'
import Scopes from './collections/Scopes'
import SubscriptionPlan from './collections/SubscriptionPlans'
import SubscriptionRecord from './collections/SubscriptionRecords/SubscriptionRecords'
import Tags from './collections/Tags'
import Users from './collections/Users'
import { migrations } from './migrations'
import { ReleaseCreditAnnualPlan } from './tasks/releaseCreditAnnualPlan'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname, 'payloadCMS'),
    },
  },
  collections: [
    Users,
    Media,
    PublicUsers,
    Posts,
    Categories,
    Tags,
    SubscriptionPlan,
    SubscriptionRecord,
    GeneratedImages,
    Features,
    Scopes,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  routes: {
    api: '/la_api',
    admin: '/la_admin',
    graphQL: '/la_graphql',
    graphQLPlayground: '/la_graphql-playground',
  },
  db: postgresAdapter({
    pool: {
      connectionString: `postgresql://${process.env.PG_USERNAME}:${process.env.PG_PASSWORD}@${process.env.PG_HOST}:${process.env.PG_PORT}/${process.env.PG_DATABASE}`,
    },
    push: false,
    prodMigrations: process.env.NODE_ENV === 'production' ? migrations : undefined,
  }),
  graphQL: {
    disable: true,
  },
  sharp,
  telemetry: false,
  cookiePrefix: 'laAdmin',
  plugins: [
    payloadCloudPlugin(),
    seoPlugin({
      collections: ['posts'],
      uploadsCollection: 'media',
      generateTitle: ({ doc }: { doc: { title: string } }) => `${doc.title} | ColorAria`,
      generateDescription: ({ doc }: { doc: { summary: string } }) => doc.summary,
    }),
  ],
  jobs: {
    autoRun: [
      {
        cron: '5 0 * * *',
        queue: 'nightly',
      },
    ],
    tasks: [ReleaseCreditAnnualPlan],
  },
})
