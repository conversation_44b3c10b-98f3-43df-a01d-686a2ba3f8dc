import { StatusCodes } from 'http-status-codes'
import { headers } from 'next/headers'
import { NextRequest } from 'next/server'
import { getPayload } from 'payload'
import Stripe from 'stripe'
import SubscriptionPlanDAO from '@/app/(app)/_backend/common/dao/SubscriptionPlanDAO'
import SubscriptionRecordsDAO from '@/app/(app)/_backend/common/dao/SubscriptionRecordsDAO'
import UserDAO from '@/app/(app)/_backend/common/dao/UserDAO'
import { HTTPException } from '@/app/(app)/_backend/common/exception'
import StripeService from '@/app/(app)/_backend/common/service/StripeService'
import { stripe } from '@/lib/stripe'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET

export async function POST(request: NextRequest) {
  try {
    const stripeSignature = (await headers()).get('stripe-signature')

    if (!stripeSignature || !webhookSecret) {
      console.error('Stripe signature or webhook secret is missing')
      throw new HTTPException('Invalid request', 400)
    }

    const event = stripe.webhooks.constructEvent(
      await request.text(),
      stripeSignature,
      webhookSecret,
    )

    const payload = await getPayload({ config })
    const subscriptionPlanDAO = new SubscriptionPlanDAO(payload)
    const publicUserDAO = new UserDAO(payload)
    const subscriptionRecordDAO = new SubscriptionRecordsDAO(payload)
    const stripeService = new StripeService(
      subscriptionPlanDAO,
      publicUserDAO,
      subscriptionRecordDAO,
    )

    // As Stripe webhooks request that we acknowledge receipt quickly, we handle the logic without awaiting
    // the completion of the update methods. This allows us to respond to Stripe promptly while processing
    // the updates in the background.
    // Documentation: https://docs.stripe.com/webhooks#acknowledge-events-immediately
    switch (event.type) {
      case 'checkout.session.completed':
        console.info('-- CHECKOUT SESSION COMPLETED EVENT RECEIVED --')
        await stripeService.updateSubscriptionRecordOnCheckoutSessionSuccess(event)
        // Track payment success event
        try {
          const session = event.data.object as Stripe.Checkout.Session
          const posthog = PostHogClient()
          posthog.capture({
            event: 'payment_success',
            distinctId: session.client_reference_id || session.customer as string,
            properties: {
              sessionId: session.id,
              amountTotal: session.amount_total,
              currency: session.currency,
              paymentStatus: session.payment_status
            }
          })
        } catch (error) {
          console.error('PostHog tracking error:', error)
        }
        break
      case 'invoice.paid':
        // Handle invoice paid event if needed
        console.info('-- INVOICE PAID EVENT RECEIVED --')
        await stripeService.updateSubscriptionRecordOnInvoicePaid(event)
        // Track recurring payment success
        try {
          const invoice = event.data.object as Stripe.Invoice
          const posthog = PostHogClient()
          posthog.capture({
            event: 'recurring_payment_success',
            distinctId: invoice.customer as string,
            properties: {
              invoiceId: invoice.id,
              amountPaid: invoice.amount_paid,
              currency: invoice.currency,
              subscriptionId: typeof invoice.subscription === 'string' ? invoice.subscription : invoice.subscription?.id
            }
          })
        } catch (error) {
          console.error('PostHog tracking error:', error)
        }
        break
      case 'customer.subscription.updated':
        console.info('-- CUSTOMER SUBSCRIPTION UPDATED RECEIVED --')
        await stripeService.updateCustomerSubscriptionOnSubscriptionUpdated(event)
        // Track subscription update
        try {
          const subscription = event.data.object as Stripe.Subscription
          const posthog = PostHogClient()
          posthog.capture({
            event: 'subscription_updated',
            distinctId: subscription.customer as string,
            properties: {
              subscriptionId: subscription.id,
              status: subscription.status,
              currentPeriodEnd: subscription.current_period_end
            }
          })
        } catch (error) {
          console.error('PostHog tracking error:', error)
        }
        break
      case 'invoice.payment_failed':
        console.info('-- INVOICE PAYMENT FAILED EVENT RECEIVED --')
        await stripeService.updateSubscriptionRecordOnPaymentFailed(event)
        // Track payment failure
        try {
          const invoice = event.data.object as Stripe.Invoice
          const posthog = PostHogClient()
          posthog.capture({
            event: 'payment_failed',
            distinctId: invoice.customer as string,
            properties: {
              invoiceId: invoice.id,
              amountDue: invoice.amount_due,
              currency: invoice.currency,
              subscriptionId: typeof invoice.subscription === 'string' ? invoice.subscription : invoice.subscription?.id
            }
          })
        } catch (error) {
          console.error('PostHog tracking error:', error)
        }
        break
      case 'customer.subscription.deleted':
        console.info('-- CUSTOMER SUBSCRIPTION DELETED RECEIVED --')
        await stripeService.updateCustomerSubscriptionOnSubscriptionDeleted(event)
        // Track subscription cancellation
        try {
          const subscription = event.data.object as Stripe.Subscription
          const posthog = PostHogClient()
          posthog.capture({
            event: 'subscription_cancelled',
            distinctId: subscription.customer as string,
            properties: {
              subscriptionId: subscription.id,
              canceledAt: subscription.canceled_at,
              cancelAtPeriodEnd: subscription.cancel_at_period_end
            }
          })
        } catch (error) {
          console.error('PostHog tracking error:', error)
        }
        break
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    if (error instanceof Stripe.errors.StripeError) {
      if (error instanceof Stripe.errors.StripeSignatureVerificationError) {
        // For signature verification errors, tell Stripe not to retry
        return new Response(JSON.stringify({ error: 'Invalid signature' }), {
          status: StatusCodes.UNAUTHORIZED,
          headers: {
            'Content-Type': 'application/json',
          },
        })
      } else {
        // For other Stripe errors, you might want to retry
        console.error('Stripe error:', error.message)
        return new Response(JSON.stringify({ error: 'Stripe error occurred' }), {
          status: StatusCodes.INTERNAL_SERVER_ERROR,
          headers: {
            'Content-Type': 'application/json',
          },
        })
      }
    }

    // For all other errors, log and acknowledge receipt
    console.error('Error in webhook handling:', error)
    return new Response(JSON.stringify({ received: false, error: 'Error in webhook handling' }), {
      status: StatusCodes.INTERNAL_SERVER_ERROR,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
