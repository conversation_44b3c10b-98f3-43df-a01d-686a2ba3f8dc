import { StatusCodes } from 'http-status-codes'
import * as jose from 'jose'
import { NextResponse } from 'next/server'
import { getPayload, getCookieExpiration } from 'payload'
import UserDAO from '@/app/(app)/_backend/common/dao/UserDAO'
import { HTTPException } from '@/app/(app)/_backend/common/exception'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import { _verifyJWT } from '@/app/(app)/_backend/common/utils/auth'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'

export async function GET(request: Request) {
  try {
    const payload = await getPayload({ config })
    const secret = new TextEncoder().encode(payload.secret)
    // 1. Extract code from query params
    const url = new URL(request.url)
    const code = url.searchParams.get('code')
    const state = url.searchParams.get('state')
    console.log(state)

    let fingerprintHash = ''
    let networkFingerprintHash = ''
    let redirectOnFail = '/login'

    if (state) {
      try {
        const result = await _verifyJWT(state)
        fingerprintHash = result.fingerprintHash as string
        networkFingerprintHash = result.networkFingerprintHash as string
        redirectOnFail = result.redirectOnFail as string
      } catch (err) {
        throw new HTTPException(
          'Google auth has expired, please try logging in again',
          StatusCodes.BAD_GATEWAY,
          '/login',
        )
      }
    }

    if (!code) {
      throw new HTTPException('Missing code.', StatusCodes.BAD_REQUEST, redirectOnFail)
    }

    // 2. Exchange code for tokens and user info
    const tokenRes = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        code,
        client_id: process.env.GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        redirect_uri: process.env.GOOGLE_CALLBACK_URL!,
        grant_type: 'authorization_code',
      }),
    })
    const tokenData = await tokenRes.json()
    if (!tokenData.access_token) {
      throw new HTTPException(
        'Failed to retrieve access token.',
        StatusCodes.BAD_REQUEST,
        redirectOnFail,
      )
    }

    // 3. Get user info from Google
    const userRes = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: { Authorization: `Bearer ${tokenData.access_token}` },
    })
    const { email, given_name, family_name } = await userRes.json()
    console.log(email, given_name, family_name)

    // 4. Find or create user in your DB
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)
    let user = await userService.getUserByEmailId(email)
    if (!user) {
      user = await userService.register(
        email,
        given_name,
        family_name,
        true,
        undefined,
        networkFingerprintHash,
        fingerprintHash,
      )
    }

    // 5. Issue JWT
    const collectionConfig = payload.collections['public-users'].config
    const fieldsToSign = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      freeCredits: user.freeCredits,
      paidCredits: user.paidCredits,
      rolloverCredits: user.rolloverCredits,
      role: user.role,
      collection: collectionConfig.slug,
    }

    // Calculate expiration timestamp
    const expirationTime = Math.floor(Date.now() / 1000) + collectionConfig.auth.tokenExpiration
    // Sign the token using jose
    const token = await new jose.SignJWT(fieldsToSign)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(expirationTime) // Use numeric timestamp
      .sign(secret)

    const expiresAt = getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration })
    const expTimestamp = Math.floor(expiresAt.getTime() / 1000)
    const authState = {
      state: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          freeCredits: user.freeCredits,
          paidCredits: user.paidCredits,
          rolloverCredits: user.rolloverCredits,
          role: user.role,
        },
        hasEverLoggedIn: true,
        isLoggedIn: true,
        isLoading: false,
        error: null,
        expiry: expTimestamp,
      },
      version: 0,
    }

    // Track OAuth login event
    try {
      const posthog = PostHogClient()
      posthog.capture({
        event: 'oauth_login',
        distinctId: user.email,
        properties: {
          userId: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          provider: 'google'
        }
      })
    } catch (error) {
      console.error('PostHog tracking error:', error)
    }

    // 7. Redirect or respond
    const origin =
      request.headers.get('origin') || process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
    const response = NextResponse.redirect(origin + '/')
    response.cookies.set('auth-storage', JSON.stringify(authState), {
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
      expires: getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration }),
    })
    response.cookies.set('la-token', token, {
      httpOnly: true,
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
      expires: getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration }),
    })
    return response
  } catch (error) {
    const origin =
      request.headers.get('origin') || process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
    if (error instanceof HTTPException) {
      const redirectOnFail = error.redirect || '/login'
      const message = error.message
      return NextResponse.redirect(
        `${origin}${redirectOnFail}?error=oauth_failed&message=${encodeURIComponent(message)}`,
      )
    } else {
      return NextResponse.redirect(
        `${origin}/login?error=oauth_failed&message=${encodeURIComponent('Something went wrong')}`,
      )
    }
  }
}
