import { getPayload } from 'payload'
import { errorHand<PERSON> } from '@/app/(app)/_backend/common/exception/errorHandler'
import { s3Client } from '@/lib/s3-client'
import config from '@payload-config'

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ imageId: string }> },
) {
  try {
    const { imageId } = await params
    const payload = await getPayload({ config })
    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    if (!imageId) {
      return new Response('Image ID is required', { status: 400 })
    }

    payload.delete({
      collection: 'generated-images',
      where: {
        id: {
          equals: imageId,
        },
        createdBy: {
          equals: user.id,
        },
      },
    })
    s3Client.deleteObject(imageId, undefined, `${user.id}/`)

    return new Response(null, { status: 204 })
  } catch (error) {
    return errorHandler(error)
  }
}
