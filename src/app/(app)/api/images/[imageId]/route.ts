import { getPayload } from 'payload'
import { errorH<PERSON><PERSON> } from '@/app/(app)/_backend/common/exception/errorHandler'
import { s3Client } from '@/lib/s3-client'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ imageId: string }> },
) {
  try {
    const { imageId } = await params
    const payload = await getPayload({ config })
    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    if (!imageId) {
      return new Response('Image ID is required', { status: 400 })
    }

    payload.delete({
      collection: 'generated-images',
      where: {
        id: {
          equals: imageId,
        },
        createdBy: {
          equals: user.id,
        },
      },
    })
    s3Client.deleteObject(imageId, undefined, `${user.id}/`)

    // Track image deletion event
    try {
      const posthog = PostHogClient()
      posthog.capture({
        event: 'image_deleted',
        distinctId: user.id,
        properties: {
          imageId
        }
      })
    } catch (error) {
      console.error('PostHog tracking error:', error)
    }

    return new Response(null, { status: 204 })
  } catch (error) {
    return errorHandler(error)
  }
}
