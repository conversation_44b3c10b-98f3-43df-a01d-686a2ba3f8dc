import { getPayload } from 'payload'
import FeaturesDAO from '@/app/(app)/_backend/common/dao/FeaturesDAO'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import UserDAO from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import FeatureScopeService from '@/app/(app)/_backend/common/service/FeatureScopeService'
import ImageService from '@/app/(app)/_backend/common/service/ImageService'
import UnsplashService from '@/app/(app)/_backend/common/service/UnsplashService'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import { FeaturesType } from '@/utilities/local/enums'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'

// type Quality = 'LOW_QUALITY' | 'HIGH_QUALITY'
type I2IAspectRatio = '1:1' | '2:3' | '3:2'

interface ImageGenerationRequestI2I {
  imageBase64: string
  aspectRatio: I2IAspectRatio
  numberOfImages: number
  unsplashSrc?: string
}

export interface ImageGenerationResponseI2I {
  remainingCredits: number
}

const mandatoryFeatures = [
  FeaturesType.IMAGE_TO_COLOR,
  FeaturesType.IMAGE_TO_COLOR_IMAGE_REPETITION,
]

export async function POST(request: Request) {
  try {
    const { imageBase64, aspectRatio, numberOfImages, unsplashSrc } =
      (await request.json()) as ImageGenerationRequestI2I

    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)
    const payload = await getPayload({ config })

    const userDAO = new UserDAO(payload)
    const featuresDAO = new FeaturesDAO(payload)
    const userService = new UserService(userDAO)
    const featureScopeService = new FeatureScopeService(userDAO, featuresDAO)

    const costTabulation = await featureScopeService.costTabulation(user.id, mandatoryFeatures)
    const remainingCredits = (await userService.deductCredit(user.id, costTabulation)).freeCredits

    //TODO: DETERMINE THE QUALITY FROM USER'S MEMBERSHIP
    const quality = 'HIGH_QUALITY'

    console.log(unsplashSrc)

    if (unsplashSrc != '' && unsplashSrc != null) {
      const unsplashService = new UnsplashService()
      unsplashService.attributionLink(unsplashSrc) // this is event-based fire and forget, no response required.
    }

    const generatedImageDAO = new GeneratedImageDAO(payload)

    const imageService = new ImageService(generatedImageDAO)
    imageService.imageToImage(imageBase64, aspectRatio, quality, numberOfImages, user.id)

    // Track image-to-image generation event
    try {
      const posthog = PostHogClient()
      posthog.capture({
        event: 'image_to_image_generation',
        distinctId: user.id,
        properties: {
          aspectRatio,
          quality,
          numberOfImages,
          hasUnsplashSource: !!unsplashSrc,
          remainingCredits
        }
      })
    } catch (error) {
      console.error('PostHog tracking error:', error)
    }

    // note: not returning the image
    return new Response(
      JSON.stringify({
        message: 'Generating images...',
        data: {
          remainingCredits,
        },
      }),
      {
        status: 202,
      },
    )
  } catch (error) {
    return errorHandler(error)
  }
}
