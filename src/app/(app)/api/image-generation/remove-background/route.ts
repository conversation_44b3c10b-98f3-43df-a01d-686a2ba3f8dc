import { getPayload } from 'payload'
import FeaturesDAO from '@/app/(app)/_backend/common/dao/FeaturesDAO'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import UserDAO from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import FeatureScopeService from '@/app/(app)/_backend/common/service/FeatureScopeService'
import ImageService from '@/app/(app)/_backend/common/service/ImageService'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import { FeaturesType } from '@/utilities/local/enums'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'

type AspectRatio = '1:1' | '2:3' | '3:2'

interface RemoveBackgroundRequest {
  imageBase64: string
  aspectRatio: AspectRatio
}

export interface RemoveBackgroundResponse {
  remainingCredits: number
}

const mandatoryFeatures = [FeaturesType.AI_BACKGROUND_REMOVER]

export async function POST(request: Request) {
  try {
    const { imageBase64, aspectRatio } = (await request.json()) as RemoveBackgroundRequest

    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    const payload = await getPayload({ config })

    const userDAO = new UserDAO(await getPayload({ config }))
    const featuresDAO = new FeaturesDAO(await getPayload({ config }))
    const userService = new UserService(userDAO)
    const featureScopeService = new FeatureScopeService(userDAO, featuresDAO)

    const costTabulation = await featureScopeService.costTabulation(user.id, mandatoryFeatures)
    const remainingCredits = (await userService.deductCredit(user.id, costTabulation)).freeCredits

    const generatedImageDAO = new GeneratedImageDAO(payload)

    const imageService = new ImageService(generatedImageDAO)
    imageService.removeBackground(imageBase64, aspectRatio, user.id)

    // Track background removal event
    try {
      const posthog = PostHogClient()
      posthog.capture({
        event: 'background_removal',
        distinctId: user.id,
        properties: {
          aspectRatio,
          remainingCredits
        }
      })
    } catch (error) {
      console.error('PostHog tracking error:', error)
    }

    // note: not returning the image
    return new Response(
      JSON.stringify({ message: 'Generating images...', data: { remainingCredits } }),
      {
        status: 202,
      },
    )
  } catch (error) {
    return errorHandler(error)
  }
}
