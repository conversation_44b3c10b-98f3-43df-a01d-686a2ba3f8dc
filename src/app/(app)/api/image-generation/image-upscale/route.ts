import { getPayload } from 'payload'
import FeaturesDAO from '@/app/(app)/_backend/common/dao/FeaturesDAO'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import UserDAO from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import FeatureScopeService from '@/app/(app)/_backend/common/service/FeatureScopeService'
import ImageService from '@/app/(app)/_backend/common/service/ImageService'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import { FeaturesType } from '@/utilities/local/enums'
import config from '@payload-config'

interface ImageUpscaleRequest {
  b64: string
  upscale: number
}

export interface ImageUpscaleResponse {
  src: string
  width: number
  height: number
  remainingCredits: number
}

const mandatoryFeatures = [FeaturesType.TRADITIONAL_UPSCALER]

export async function POST(request: Request) {
  try {
    const { b64, upscale } = (await request.json()) as ImageUpscaleRequest

    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    const payload = await getPayload({ config })
    const userDAO = new UserDAO(await getPayload({ config }))
    const featuresDAO = new FeaturesDAO(await getPayload({ config }))
    const userService = new UserService(userDAO)
    const featureScopeService = new FeatureScopeService(userDAO, featuresDAO)

    const costTabulation = await featureScopeService.costTabulation(user.id, mandatoryFeatures)
    const remainingCredits = (await userService.deductCredit(user.id, costTabulation)).freeCredits

    const generatedImageDAO = new GeneratedImageDAO(payload)

    const imageService = new ImageService(generatedImageDAO)
    await imageService.upscaleImage(b64, upscale, user.id)
    return new Response(
      JSON.stringify({
        message: 'Image has been successfully upscaled!',
        data: { remainingCredits },
      }),
    )
  } catch (error) {
    return errorHandler(error)
  }
}
