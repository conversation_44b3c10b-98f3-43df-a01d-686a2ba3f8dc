import { Cat } from 'lucide-react'
import { Metadata } from 'next'
import NextImage from 'next/image'
import Link from 'next/link'
import { getPayload } from 'payload'
import { Spacing } from '@/utilities/local/css'
import { capitalizeAndReplace } from '@/utilities/local/string'
import config from '@payload-config'
import CategoryDAO from '../../_backend/common/dao/CategoryDAO'
import PostDAO from '../../_backend/common/dao/PostDAO'
import CategoryService from '../../_backend/common/service/CategoryService'
import PostService from '../../_backend/common/service/PostService'
import DynamicIcon from '../../_component/DynamicIcon'
import ServerPagination from '../../_component/Pagination/ServerPagination'
import PillTag from '../../_component/PillTag'
import Text from '../../_component/Text'
import Title, { HeaderLevel } from '../../_component/Title'
import Container from '../../_cssComp/Container'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer, {
  ColSpan,
  GridContentAlignment,
  GridItem,
  GridItemAlignment,
} from '../../_cssComp/GridContainer'
import BlogCard from '../../_templates/Blog/BlogCard'

export async function generateMetadata({
  params,
}: {
  params: Promise<{ category: string }>
}): Promise<Metadata> {
  const { category: categorySlug } = await params
  const capitalizeCategory = capitalizeAndReplace(categorySlug)
  return {
    title: `${capitalizeCategory} Blog | ColorAria`,
    description: `Explore our ${capitalizeCategory} content`,
    keywords: [capitalizeCategory, 'tutorials', 'ColorAria', 'digital art', 'guides'],
    openGraph: {
      title: `${capitalizeCategory} Blog | ColorAria`,
      description: `Explore our ${capitalizeCategory} content`,
      siteName: 'ColorAria',
      type: 'website',
      url: `${process.env.NEXT_PUBLIC_HOST}/blog/${categorySlug.toLowerCase()}`,
    },
    twitter: {
      card: 'summary_large_image',
      title: `${capitalizeCategory} Blog | ColorAria`,
      site: `ColorAria`,
      description: `Explore our ${capitalizeCategory} content`,
    },
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_HOST}/blog/${categorySlug.toLowerCase()}`,
    },
  }
}

interface ServerProps {
  params: Promise<{ category: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

async function BlogCategoryPage(serverProps: ServerProps) {
  const { category: categorySlug } = await serverProps.params

  const receivedSearchParams = await serverProps.searchParams
  const currentPageQuery = receivedSearchParams.page ? Number(receivedSearchParams.page) : 1
  const payload = await getPayload({ config })
  const postDAO = new PostDAO(payload)
  const postService = new PostService(postDAO)
  const categoryDAO = new CategoryDAO(payload)
  const categoryService = new CategoryService(categoryDAO)
  const allCategories = await categoryService.getAllCategories()
  const mappedCategory = allCategories.find(
    (category) => category.name === capitalizeAndReplace(categorySlug),
  )
  const queryParams = {
    category: mappedCategory?.id,
  }
  const allPosts = await postService.getAllPages(currentPageQuery, 10, queryParams)
  const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

  //REFERENCE FROM: https://blog.kristi.digital/

  return (
    <div className={`bg-main min-h-screen flex flex-col`}>
      <div className="bg-accent1">
        <Container>
          <GridContainer
            columns={12}
            contentAlign={GridContentAlignment.CENTER}
            className={`h-[32dvh] w-full ${Spacing.ContentPadding} gap-8 `}
          >
            <GridItem colSpan={ColSpan.SPAN_6}>
              <FlexContainer className="w-full h-full" justify={JustifyContent.START}>
                <NextImage
                  style={{
                    width: '500px',
                    height: '357px',
                  }}
                  src={`/media/static/illustration/blogIllust.svg`}
                  width={500}
                  height={357}
                  alt="Blog Image"
                />
              </FlexContainer>
            </GridItem>
            <GridItem colSpan={ColSpan.SPAN_6}>
              <FlexContainer
                direction={FlexDirection.COL}
                className="w-full h-full gap-4"
                align={AlignItems.START}
                justify={JustifyContent.CENTER}
              >
                <Title level={HeaderLevel.H1} className="text-8xl">
                  Blog //
                </Title>
                <Title level={HeaderLevel.H1} className="text-4xl">
                  Announcements, Updates & More!
                </Title>
                <span className="text-2xl">Get all your latest updates here!</span>
              </FlexContainer>
            </GridItem>
          </GridContainer>
        </Container>
      </div>
      <div className="w-full gap-4 border-t-4 border-b-4 border-black bg-accent4-lighter">
        <Container>
          <FlexContainer direction={FlexDirection.ROW} className="w-full gap-4">
            <Link href={`${HOST_URL}/blog`}>
              <PillTag
                isButton
                text={
                  <FlexContainer
                    className="gap-2"
                    align={AlignItems.CENTER}
                    direction={FlexDirection.ROW}
                  >
                    <Cat /> All
                  </FlexContainer>
                }
                className={'bg-accent1 hover:bg-accent1-lighter'}
              />
            </Link>
            {allCategories &&
              allCategories.map((category, i) => {
                //const categoryName = category.name!
                // const newUrl = new URLSearchParams({
                //   ...receivedSearchParams,
                //   category: categoryName,
                // })
                return (
                  <Link key={i} href={`${HOST_URL}/blog/${category.name?.toLowerCase()}`}>
                    <PillTag
                      key={i}
                      isButton
                      text={
                        <FlexContainer className="gap-2" align={AlignItems.CENTER}>
                          <DynamicIcon iconName={category.icon} />
                          {category.name}
                        </FlexContainer>
                      }
                      className={'bg-accent1 hover:bg-accent1-lighter'}
                    />
                  </Link>
                )
              })}
          </FlexContainer>
        </Container>
      </div>
      <Container className="flex-1 flex flex-col w-full h-full">
        <FlexContainer direction={FlexDirection.COL} className={'gap-4 mt-2 w-full h-full'}>
          <GridContainer className={`w-full h-full grid-cols-2`}>
            <GridItem colSpan={ColSpan.SPAN_1} alignSelf={GridItemAlignment.START}>
              <Title level={HeaderLevel.H2}>
                {categorySlug ? capitalizeAndReplace(categorySlug) : 'All'} Posts
              </Title>
            </GridItem>
            <GridItem colSpan={ColSpan.SPAN_1} alignSelf={GridItemAlignment.END}>
              <ServerPagination
                currentPage={currentPageQuery}
                totalPage={allPosts.totalPages}
                slug={`/blog/${categorySlug.toLowerCase()}`}
                queryKey="page"
                queryParams={receivedSearchParams}
              />
            </GridItem>
          </GridContainer>

          {allPosts.docs.length === 0 ? (
            <FlexContainer
              className="w-full h-full gap-2 flex-1"
              direction={FlexDirection.COL}
              align={AlignItems.CENTER}
              justify={JustifyContent.CENTER}
            >
              <NextImage
                src="/media/static/illustration/categoryNotFound.svg"
                alt="No post found in this category"
                width={300}
                height={300}
              />
              <Title level={HeaderLevel.H3} className="text-center">
                No posts found in this category.
              </Title>
              <Text className="text-center" variant="description">
                {`Let's try some other categories!`}
              </Text>
            </FlexContainer>
          ) : (
            <GridContainer className="w-full h-full flex-1 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 gap-8">
              {allPosts.docs.map((post, i) => {
                return (
                  <GridItem key={i} colSpan={ColSpan.SPAN_1}>
                    <Link href={`${HOST_URL}/blog/${categorySlug.toLowerCase()}/${post.slug}`}>
                      <BlogCard post={post} descriptionLimit={20} />
                    </Link>
                  </GridItem>
                )
              })}
            </GridContainer>
          )}

          {/* <GridContainer className="w-full h-full flex-1 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 gap-8">
            {allPosts.docs.map((post, i) => {
              return (
                <GridItem key={i} colSpan={ColSpan.SPAN_1}>
                  <Link href={`${HOST_URL}/blog/${post.slug}`}>
                    <BlogCard post={post} descriptionLimit={20} />
                  </Link>
                </GridItem>
              )
            })}
          </GridContainer> */}
        </FlexContainer>
      </Container>
    </div>
  )
}

export default BlogCategoryPage
