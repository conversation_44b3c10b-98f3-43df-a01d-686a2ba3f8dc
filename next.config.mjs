import { withPayload } from '@payloadcms/next/withPayload'

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Your Next.js config here
  // output: process.env.NODE_ENV === 'production'? 'standalone':'export',
  output: process.env.NODE_ENV === 'production'? 'standalone':'standalone',
  compress: process.env.NODE_ENV === 'production',
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '', // optional, empty if not used
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '*.5c5d21674c964c960b5a5786d22e73e2.r2.cloudflarestorage.com',
        port: '', // optional, empty if not used
        pathname: '/**',
      },
    ],
  },
  // for hot reloading in docker container
  // exclude node_modules from watching, so need to restart after adding new dependencies
  webpack: (config, _) => ({
    ...config,
    watchOptions:
      process.env.NODE_ENV === 'development'
        ? {
            ...config.watchOptions,
            ignored: ['**/node_modules'],
            poll: 1000,
            aggregateTimeout: 300,
          }
        : undefined,
  }),
  // async rewrites() {
  //   return [
  //     {
  //       source: "/relay-Fifh/static/:path*",
  //       destination: "https://eu-assets.i.posthog.com/static/:path*",
  //     },
  //     {
  //       source: "/relay-Fifh/:path*",
  //       destination: "https://eu.i.posthog.com/:path*",
  //     },
  //     {
  //       source: "/relay-Fifh/flags",
  //       destination: "https://eu.i.posthog.com/flags",
  //     },
  //   ];
  // },
  // // This is required to support PostHog trailing slash API requests
  // skipTrailingSlashRedirect: true, 
}

export default withPayload(nextConfig)
